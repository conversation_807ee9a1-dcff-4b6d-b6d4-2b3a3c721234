/* ===== Base Reset & Typography ===== */
body {
  margin: 0;
  padding: 0;
  font-family: 'Segoe UI', 'Roboto', sans-serif;
  background-color: #f4f1ea;
  color: #2c2c2c;
  line-height: 1.6;
}

/* ===== Titles ===== */
.title-container {
  text-align: center;
  margin-top: 60px;
  margin-bottom: 30px;
}

.title-container h1 {
  font-size: 2.8rem;
  color: #c6a664;
  margin-bottom: 12px;
  font-weight: 600;
  letter-spacing: 1px;
}

.title-container p {
  font-size: 1.2rem;
  color: #6c6c6c;
}

/* ===== Form Wrapper ===== */
.form {
  max-width: 1300px;
  width: 90%;
  margin: 40px auto;
  padding: 40px;
  background-color: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.05);
}

/* ===== Drink Display ===== */
.drink-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;
}

.mini-header {
  font-size: 1.8rem;
  color: #c6a664;
  margin-right: 10px;
}

/* ===== Layout ===== */
.container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 24px;
  margin-bottom: 30px;
}

/* ===== Input Card ===== */
.mini-container {
  background-color: #faf8f3;
  border-radius: 12px;
  padding: 24px 20px;
  width: 260px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.06);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.mini-container h3 {
  font-size: 1.1rem;
  color: #c6a664;
  margin-bottom: 12px;
}

/* ===== Answer Space ===== */
.answer-space {
  width: 120px;
  height: 36px;
  border-radius: 8px;
  background-color: #f4f1ea;
  border: 2px solid #ddd6c0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  font-size: 1rem;
  margin: 12px 0 18px 0;
  transition: all 0.3s ease;
}

#correct {
  background-color: #e6f5e9;
  color: #2d7032;
  border-color: #58b86b;
}

#wrong {
  background-color: #fcebea;
  color: #a94442;
  border-color: #e39a9a;
}

/* ===== Choices ===== */
.radio-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
}

.radio-buttons li {
  list-style: none;
  display: flex;
  align-items: center;
  font-size: 0.95rem;
  color: #3c3c3c;
}

.radio-buttons input[type="radio"] {
  appearance: none;
  width: 18px;
  height: 18px;
  border: 2px solid #c6a664;
  border-radius: 50%;
  margin-right: 10px;
  cursor: pointer;
  position: relative;
}

.radio-buttons input[type="radio"]:checked::before {
  content: '';
  position: absolute;
  width: 10px;
  height: 10px;
  top: 3px;
  left: 3px;
  background-color: #c6a664;
  border-radius: 50%;
}

/* ===== Buttons ===== */
.button {
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 600;
  border: none;
  border-radius: 8px;
  background-color: #c6a664;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 12px 8px;
}

.button:hover {
  background-color: #d9bb80;
  color: #2c2c2c;
}

.button.submit {
  background-color: #2c2c2c;
  color: #f4f1ea;
}

.button.submit:hover {
  background-color: #444;
  color: #f4f1ea;
}

.button.newdrink {
  background-color: #faf8f3;
  color: #c6a664;
  border-radius: 50%;
  padding: 0.6em 0.8em;
  font-size: 1.3em;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

.button.newdrink:hover {
  background-color: #c6a664;
  color: #fff;
}

/* ===== Center Button Group ===== */
.form > .button,
.form > .button.submit {
  display: block;
  margin: 16px auto;
}

/* ===== Responsive ===== */
@media (max-width: 768px) {
  .container {
    flex-direction: column;
    align-items: center;
  }

  .mini-container {
    width: 100%;
    max-width: 360px;
  }
}

/* ===== Textbox Styling ===== */
.textbox-container {
  width: 100%;
  text-align: center;
}

.textbox {
  width: 90%;
  max-width: 180px;
  padding: 10px 12px;
  border: 2px solid #c6a664;
  border-radius: 6px;
  font-size: 1rem;
  margin-bottom: 12px;
  text-align: center;
  background-color: #f4f1ea;
  color: #2c2c2c;
  outline: none;
  transition: border 0.3s;
}

.textbox:focus {
  border-color: #a08953;
}

/* Optional: display possible values */
.choices-list {
  list-style: none;
  padding: 0;
  margin: 0;
  color: #888;
  font-size: 0.9rem;
}

.choice-item {
  text-transform: capitalize;
}
