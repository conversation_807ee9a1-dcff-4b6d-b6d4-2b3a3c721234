export default function BanList({ list, onRemove }) {
  return (
    <div>
      <h3 className="text-xl font-bold mb-2">Ban List</h3>
      <p className="text-sm mb-4">Select an attribute in your listing to ban it</p>
      <div className="flex flex-col gap-2">
        {list.map((kw) => (
          <button
            key={kw}
            onClick={() => onRemove(kw)}
            className="bg-rose-500 hover:bg-rose-600 text-white py-1 rounded"
          >
            {kw}
          </button>
        ))}
      </div>
    </div>
  );
}
