export default function BanList({ list, onRemove }) {
  return (
    <div>
      <h3 className="text-xl font-bold mb-2 text-white">Ban List</h3>
      <p className="text-sm mb-6 text-gray-400">Select an attribute in your listing to ban it</p>
      <div className="flex flex-col gap-2 max-h-[calc(100vh-140px)] overflow-y-auto">
        {list.length === 0 ? (
          <p className="text-gray-500 text-sm italic">No banned attributes yet...</p>
        ) : (
          list.map((attribute, index) => (
            <button
              key={`${attribute}-${index}`}
              onClick={() => onRemove(attribute)}
              className="bg-gradient-to-r from-rose-500 to-red-500 hover:from-rose-600 hover:to-red-600 text-white py-2 px-3 rounded-full text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105 text-left"
            >
              <span className="mr-1 text-xs">✕</span>
              <span className="text-xs">{attribute}</span>
            </button>
          ))
        )}
      </div>
    </div>
  );
}
