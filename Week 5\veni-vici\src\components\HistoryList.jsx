export default function HistoryList({ items }) {
  return (
    <div>
      <h3 className="text-xl font-bold mb-6 text-white">Who have we seen so far?</h3>
      <div className="flex flex-col gap-4 max-h-[calc(100vh-120px)] overflow-y-auto">
        {items.length === 0 ? (
          <p className="text-gray-400 text-sm italic">No cats discovered yet...</p>
        ) : (
          items.map((cat, i) => (
            <div key={`${cat.id}-${i}`} className="flex gap-3 items-center p-3 bg-gray-800/30 rounded-lg hover:bg-gray-800/50 transition-colors duration-200">
              <img
                src={cat.image}
                alt={cat.name}
                className="w-12 h-12 rounded-full object-cover border-2 border-gray-600 flex-shrink-0"
              />
              <div className="flex-1 min-w-0">
                <p className="text-sm text-gray-200 leading-relaxed">
                  {cat.description}
                </p>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
