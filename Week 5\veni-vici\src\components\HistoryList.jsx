export default function HistoryList({ items }) {
  return (
    <div>
      <h3 className="text-xl font-bold mb-4">Who have we seen so far?</h3>
      <div className="flex flex-col gap-4">
        {items.map((item, i) => {
          const { title } = item.data[0];
          const img = item.links[0].href;
          return (
            <div key={i} className="flex gap-2 items-center">
              <img src={img} alt={title} className="w-12 h-12 rounded object-cover" />
              <p className="text-sm">{title}</p>
            </div>
          );
        })}
      </div>
    </div>
  );
}
