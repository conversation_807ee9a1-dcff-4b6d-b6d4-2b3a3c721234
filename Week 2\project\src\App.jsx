import React from "react";
import minecraftBg from "./assets/minecraft_bg.jpg";
import { useState } from "react";
import Card from "./components/Card";
import obsidian from "./assets/obsidian.jpg";
import creeper from "./assets/creeper.jpg";
import bones from "./assets/bones.png";
import diamondArmor from "./assets/diamondArmour.webp";
import pickaxe from "./assets/pickaxe.png";
import end from "./assets/end.webp";
import goldenApple from "./assets/golden_apple.webp";
import steak from "./assets/steak.webp";
import polarBear from "./assets/polar_bear.webp";
import frostWalker from "./assets/frwalkar.webp";
import "./App.css";

const App = () => {
  const minecraftCards = [
    {
      question: "What material is needed to build a Nether Portal?",
      answer: "Obsidian",
      color: "rgb(11, 12, 12)",
      image: obsidian,
    },
    {
      question: "Which mob explodes when it gets close to the player?",
      answer: "Creeper",
      color: "rgb(113, 147, 75)",
      image: creeper,
    },
    {
      question: "What do you use to tame a wolf?",
      answer: "Bones",
      color: "rgb(189, 189, 189)",
      image: bones,
    },
    {
      question: "How many diamonds are needed for a full set of diamond armor?",
      answer: "24",
      color: "rgb(159, 207, 255)",
      image: diamondArmor,
    },
    {
      question: "Which tool is best for mining stone blocks quickly?",
      answer: "Pickaxe",
      color: "rgb(69, 69, 69)",
      image: pickaxe,
    },
    {
      question:
        "Which dimension contains Endermen, End Cities, and the Ender Dragon?",
      answer: "The End",
      color: "rgb(11, 12, 12)",
      image: end,
    },
    {
      question: "What item do you need to cure a zombie villager?",
      answer: "A splash potion of Weakness and a golden apple",
      color: "rgb(255, 215, 0)",
      image: goldenApple,
    },
    {
      question: "What food item restores the most hunger points?",
      answer: "Steak",
      color: "rgb(103, 46, 0)",
      image: steak,
    },
    {
      question: "Which biome do polar bears spawn in?",
      answer: "Snowy tundra or ice plains",
      color: "rgb(202, 255, 255)",
      image: polarBear,
    },
    {
      question: "What enchantment allows you to walk on water by freezing it?",
      answer: "Frost Walker",
      color: "rgb(36, 236, 236)",
      image: frostWalker,
    },
  ];

  const [currentIndex, setCurrentIndex] = useState(0);
  const [isFlipped, setIsFlipped] = useState(false);

  const handleCardClick = () => {
    setIsFlipped(!isFlipped);
  };

  const handleNextClick = () => {
    if (isFlipped) {
      setIsFlipped(false);

      setTimeout(() => {
        let nextIndex;
        do {
          nextIndex = Math.floor(Math.random() * minecraftCards.length);
        } while (nextIndex === currentIndex);

        setCurrentIndex(nextIndex);
      }, 500);
    } else {
      let nextIndex;
      do {
        nextIndex = Math.floor(Math.random() * minecraftCards.length);
      } while (nextIndex === currentIndex);

      setCurrentIndex(nextIndex);
    }
  };

  const currentCard = minecraftCards[currentIndex];

  // Function to determine text color based on background color (AI helped)
  const getContrastTextColor = (bgColor) => {
    const rgb = bgColor.match(/\d+/g).map(Number);
    const luminance = (0.299 * rgb[0] + 0.587 * rgb[1] + 0.114 * rgb[2]) / 255;
    return luminance > 0.5 ? "#000000" : "#ffffff";
  };

  const textColor = getContrastTextColor(currentCard.color);

  return (
    <>
      <img src={minecraftBg} alt="Minecraft background" className="bg" />
      <div className="main">
        {isFlipped && currentCard.image && (
          <img 
            src={currentCard.image} 
            alt={currentCard.question} 
            style={{ transform: 'scaleX(-1)', height: '250px', width: 'auto' }} 
            className="side-image"
          />
        )}
        <div
          className="container"
          style={{ backgroundColor: currentCard.color }}
        >
          <h1 style={{ color: textColor }}>Minecraft Trivia Flashcards</h1>
          <p style={{ color: textColor }}>
            Click the card to flip. Test your Minecraft knowledge!
          </p>
          <p style={{ color: textColor }}>
            Total Cards: {minecraftCards.length}
          </p>

          <Card
            frontText={currentCard.question}
            backText={currentCard.answer}
            flipped={isFlipped}
            onClick={handleCardClick}
          />

          <button onClick={handleNextClick}>Next</button>
        </div>
        {isFlipped && currentCard.image && (
          <img 
            src={currentCard.image} 
            alt={currentCard.question}
            style={{ height: '250px', width: 'auto' }}
            className="side-image"
          />
        )}
      </div>
    </>
  );
};

export default App;
