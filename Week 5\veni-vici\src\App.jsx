import { useState } from "react";
import ResultCard from "./components/ResultCard";
import HistoryList from "./components/HistoryList";
import BanList from "./components/BanList";

export default function App() {
  const [result, setResult] = useState(null);
  const [banList, setBanList] = useState([]);
  const [history, setHistory] = useState([]);

  const fetchData = async () => {
    try {
      // Fetch a random cat from TheCatAPI with your API key
      const res = await fetch('https://api.thecatapi.com/v1/images/search?limit=1&has_breeds=1', {
        headers: {
          'x-api-key': 'live_TEaOSpGMOiEkrCJjM9lU1yXfG1T1ZT1UFUqYcrfAH9auaQC56kueHB8OHrQEEyzF'
        }
      });
      const data = await res.json();

      if (!data || !data[0] || !data[0].breeds || !data[0].breeds[0]) {
        // Fallback: try again if no breed data
        return fetchData();
      }

      const catData = data[0];
      const breed = catData.breeds[0];

      // Create attributes array for filtering
      const attributes = [
        breed.name,
        breed.origin,
        breed.weight?.metric ? `${breed.weight.metric} kg` : null,
        breed.life_span ? `${breed.life_span} years` : null,
        breed.temperament?.split(', ')[0], // First temperament trait
        breed.temperament?.split(', ')[1], // Second temperament trait
      ].filter(Boolean);

      // Check if any attributes are banned
      const isBanned = attributes.some(attr => banList.includes(attr));

      if (isBanned) {
        // Try again if this cat has banned attributes
        return fetchData();
      }

      const catResult = {
        id: catData.id,
        name: breed.name,
        image: catData.url,
        breed: breed,
        attributes: attributes,
        description: `A ${breed.name} cat from ${breed.origin}`
      };

      setResult(catResult);
      setHistory((prev) => [...prev, catResult]);
    } catch (error) {
      console.error('Error fetching cat data:', error);
      alert('Failed to fetch cat data. Please try again!');
    }
  };

  const addToBan = (attribute) => {
    if (!banList.includes(attribute)) {
      setBanList([...banList, attribute]);
    }
  };

  const removeFromBan = (attribute) => {
    setBanList(banList.filter((attr) => attr !== attribute));
  };

  return (
    <div className="min-h-screen bg-[#0c0c1e] text-white font-sans">
      <div className="grid grid-cols-4 h-screen">
        {/* Left Panel - History */}
        <div className="col-span-1 p-6 overflow-y-auto border-r border-gray-700">
          <HistoryList items={history} />
        </div>

        {/* Center Panel - Current Discovery */}
        <div className="col-span-2 flex flex-col items-center justify-center text-center p-8">
          <h1 className="text-5xl font-bold mb-3 text-white">Veni Vici!</h1>
          <div className="mb-6">
            <p className="text-xl mb-2 text-gray-300">Discover cats from your wildest dreams!</p>
            <div className="text-2xl">😺😻🐱😹😽😼😺😸</div>
          </div>

          {result && (
            <ResultCard
              cat={result}
              onAttributeClick={addToBan}
            />
          )}

          <button
            onClick={fetchData}
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 mt-6 px-8 py-3 rounded-lg text-lg font-semibold transition-all duration-200 shadow-lg hover:shadow-xl"
          >
            🔄 Discover!
          </button>
        </div>

        {/* Right Panel - Ban List */}
        <div className="col-span-1 p-6 overflow-y-auto border-l border-gray-700">
          <BanList list={banList} onRemove={removeFromBan} />
        </div>
      </div>
    </div>
  );
}
