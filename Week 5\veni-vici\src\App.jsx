import { useState } from "react";
import ResultCard from "./components/ResultCard";
import HistoryList from "./components/HistoryList";
import BanList from "./components/BanList";

const keywords = ["moon", "mars", "nebula", "rover", "galaxy"];

export default function App() {
  const [result, setResult] = useState(null);
  const [banList, setBanList] = useState([]);
  const [history, setHistory] = useState([]);

  const fetchData = async () => {
    const keyword = keywords[Math.floor(Math.random() * keywords.length)];
    const res = await fetch(`https://images-api.nasa.gov/search?q=${keyword}&media_type=image`);
    const data = await res.json();
    const items = data.collection.items.filter((i) => i.data?.[0] && i.links?.[0]);

    const filtered = items.filter((item) => {
      const kws = item.data[0].keywords || [];
      return !kws.some((kw) => banList.includes(kw));
    });

    if (!filtered.length) return alert("Too many items banned!");

    const selected = filtered[Math.floor(Math.random() * filtered.length)];
    setResult(selected);
    setHistory((prev) => [...prev, selected]);
  };

  const addToBan = (kw) => {
    if (!banList.includes(kw)) setBanList([...banList, kw]);
  };

  const removeFromBan = (kw) => {
    setBanList(banList.filter((k) => k !== kw));
  };

  return (
    <div className="grid grid-cols-4 h-screen bg-gray-900 text-white">
      <div className="col-span-1 p-4 overflow-y-auto">
        <HistoryList items={history} />
      </div>

      <div className="col-span-2 flex flex-col items-center justify-center text-center p-4">
        <h1 className="text-4xl font-bold mb-2">Veni Vici! 🌌</h1>
        <p className="mb-4">Discover cats from your wildest dreams!</p>

        {result && (
          <ResultCard
            data={result.data[0]}
            img={result.links[0].href}
            onKeywordClick={addToBan}
          />
        )}

        <button
          onClick={fetchData}
          className="bg-gray-800 hover:bg-blue-500 mt-4 px-4 py-2 rounded"
        >
          🔄 Discover!
        </button>
      </div>

      <div className="col-span-1 p-4 overflow-y-auto">
        <BanList list={banList} onRemove={removeFromBan} />
      </div>
    </div>
  );
}
