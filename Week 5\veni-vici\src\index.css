@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  margin: 0;
  padding: 0;
  background: #0c0c1e;
  color: #fff;
  line-height: 1.6;
}

::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

button, .cursor-pointer {
  transition: all 0.2s ease-in-out;
}

button:focus, .cursor-pointer:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.three-panel-layout {
  display: grid;
  grid-template-columns: 300px 1fr 300px;
  width: 100vw;
  height: 100vh;
}

img {
  max-width: 100%;
  height: auto;
  object-fit: cover;
}

.history-image {
  max-width: 100%;
  height: auto;
  object-fit: cover;
}

.main-cat-image {
  width: 100%;
  max-width: 400px;
  height: 300px;
  object-fit: cover;
  border-radius: 12px;
}

.panel {
  min-height: 100vh;
  max-height: 100vh;
  overflow-y: auto;
  text-align: center;
}

@media (max-width: 1200px) {
  .three-panel-layout {
    grid-template-columns: 250px 1fr 250px;
  }

  .main-cat-image {
    max-width: 300px;
    height: 250px;
  }
}

@media (max-width: 768px) {
  .three-panel-layout {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr auto;
  }

  .main-cat-image {
    max-width: 250px;
    height: 200px;
  }
}
