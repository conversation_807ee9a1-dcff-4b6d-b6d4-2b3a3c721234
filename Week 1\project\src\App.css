.header {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 5px;
  width: 100%;
  background: linear-gradient(to right, #a18cd1, #fbc2eb);
  box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.1);
  color: #2d2d2d;
  text-align: center;
  font-size: 20px;
  margin-bottom: 20px;
}

@media (max-width: 750px) {
  .header {
    font-size: 10px;
  }
  .content-container {
    width: fit-content;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

body {
  background-color: #f5f7fa;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  margin: 0;
  padding: 0;
}

@media (max-width: 750px) {
  .header {
    font-size: 16px;
  }

  .content-container {
    display: flex;
    align-items: center;
  }
}

.content-container {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 30px;
  padding: 10px;
}

.card-container {
  background-color: #ffffff;
  padding: 0 0 18px 0;
  width: 280px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.card-container .title {
  padding: 12px 0;
  text-align: center;
  font-size: 20px;
  font-weight: 600;
  color: #2d2d2d;
}

.card-container .img {
  display: flex;
  justify-content: center;
  align-items: center;
}

.card-container .img img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  object-fit: contain;
}

.card-container .url-btn {
  margin-top: 15px;
  text-align: center;
}

.card-container .url-btn a {
  background-color: #7b61ff;
  color: white;
  text-decoration: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  display: inline-block;
}

.card-container .url-btn a:hover {
  background-color: #624ee1;
}

.footer {
  background: linear-gradient(to right, #f2f2f2, #fdfcff);
  padding: 20px;
  text-align: center;
  font-size: 16px;
  color: #333;
  border-top: 1px solid #ccc;
  margin-top: 40px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.footer p {
  margin: 0;
  line-height: 1.5;
}

.footer strong {
  color: #7b61ff;
}
