import Header from './components/Header';
import Card from './components/Card';
import Footer from './components/Footer';

import image from './assets/image.png'
import js from './assets/js.jpg'
import dp from './assets/dp.png'
import greedy from './assets/greedy.png'
import ll from './assets/ll.png'
import ccpp from './assets/ccpp.png'
import gt from './assets/gt.png'
import py from './assets/py.png'
import sort from './assets/sort.png'
import bta from './assets/bta.png'
import tc from './assets/tc.png'
import btc from './assets/btc.png'


// content should be an array of objects
const content = [
  {
    title: "Graph",
    url: "https://youtu.be/tWVWeAqZ0WU?si=frCLQmkOTcRUD-kr",
    image: image
  },
  {
    title: "Javascript - DSA",
    url: "https://youtu.be/nA2Vu0O6WqI?si=bVLJs4e1SsByl-vD",
    image: js
  },
  {
    title: "Dynamic Programming",
    url: "https://youtu.be/oBt53YbR9Kk?si=0KNZUckZAMMi3drO",
    image: dp
  },
  {
    title: "Greedy Algorithms",
    url: "https://youtu.be/bC7o8P_Ste4?si=0c7EnDO9VG96CeAR",
    image: greedy
  },
  {
    title: "Linked Lists",
    url: "https://youtu.be/Hj_rA0dhr2I?si=VwFEYAQSSM5JYeCQ",
    image: ll
  },
  {
    title: "C and C++ - DSA",
    url: "https://youtu.be/B31LgI4Y4DQ?si=cfSpehaGhsrS7dou",
    image: ccpp
  },
  {
    title: "Graph Theory Algorithms",
    url: "https://youtu.be/09_LlHjoEiY?si=nLrACwzq1uYLR_9Z",
    image: gt
  },
  {
    title: "Python - DSA",
    url: "https://youtu.be/pkYVOmU3MgA?si=bO81iByZ-JlWph3V",
    image: py
  },
  {
    title: "Sorting Algorithms",
    url: "https://youtu.be/l7-f9gS8VOs?si=7Z1KIepmqmcUBsmJ",
    image: sort
  },
  {
    title: "Binary Tree Algorithms",
    url: "https://youtu.be/fAAZixBzIAI?si=lZphnLKMPQceIVvp",
    image: bta
  },
  {
    title: "Time Complexity",
    url: "https://youtu.be/Mo4vesaut8g?si=Czl55XU7KRBciTbm",
    image: tc
  },
  {
    title: "Back Tracking",
    url: "https://youtu.be/A80YzvNwqXA?si=GDlkV4RBLnioK6vM",
    image: btc
  },
];

const App = () => {
  return (
    <>
      <Header />
      <div className="content-container">
        {content.map((item, index) => (
          <Card
            key={index}
            title={item.title}
            url={item.url}
            img={item.image}
          />
        ))}
      </div>
      <Footer/>
    </>
  );
};

export default App;
