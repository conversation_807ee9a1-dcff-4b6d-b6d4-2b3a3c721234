import "../App.css";

const Card = ({ frontText, backText, color, flipped, onClick }) => {
  return (
    <div className={`flip-card ${flipped ? "flipped" : ""}`} onClick={onClick}>
      <div className="flip-card-inner">
        <div className="flip-card-front">
          <p>{frontText}</p>
        </div>
        <div className="flip-card-back">
          <p>{backText}</p>
        </div>
      </div>
    </div>
  );
};

export default Card;
