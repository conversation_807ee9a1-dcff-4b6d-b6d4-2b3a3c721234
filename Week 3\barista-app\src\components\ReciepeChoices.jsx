import React, { Component, useEffect, useState } from "react";

// function RecipeChoices({ handleChange, label, choices, checked }) {
function RecipeChoices({ handleChange, label, choices, currentVal}) {
  return (
    <>
      {/* <div className="radio-buttons">
        {choices &&
          choices.map((choice) => (
            <li key={choice}>
              <input
                id={choice}
                value={choice}
                name={label}
                type="radio"
                onChange={handleChange}
                checked={checked == choice}
              />
              {choice}
            </li>
          ))}
      </div> */}

      <div className="textbox-container">
        <input
          type="text"
          name={label}
          value={currentVal}
          placeholder="Guess the ingredient..."
          onChange={handleChange}
          className="textbox"
        />
        <ul className="choices-list">
          {choices &&
            choices.map((choice) => (
              <li key={choice} className="choice-item">
                {choice}
              </li>
            ))}
        </ul>
      </div>
    </>
  );
}

export default RecipeChoices;
