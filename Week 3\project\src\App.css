body {
  padding: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  margin-top: 180px;
  overflow-x: hidden;
  overflow-y: auto;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  color: white;
}

.main {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 20px;
  gap: 120px;
}

.bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: -1;
}

.container {
  display: flex;
  flex-direction: column;
  justify-content: center; 
  align-items: center;    
  text-align: center;
  padding: 20px;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 10px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.5);
  max-width: 400px;
  color: white;
  min-height: 300px;
}

.flip-card {
  background-color: transparent;
  width: 300px;
  height: 120px;
  perspective: 1000px;
  margin: 20px auto;
  cursor: pointer;
}

.flip-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.5s;
  transform-style: preserve-3d;
}

.flip-card.flipped .flip-card-inner {
  transform: rotateX(180deg);
}

.flip-card-front,
.flip-card-back {
  position: absolute;
  padding: 0 10px;
  width: 100%;
  height: 100%;
  border-radius: 10px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

.flip-card-front {
  background-color: #636363;
  color: white;
}

.flip-card-back {
  background-color: rgb(159, 207, 255);
  color: rgb(0, 0, 0);
  transform: rotateX(180deg);
}

button {
  background-color: #0077ff;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 10px 20px;
  font-size: 1.2rem;
  cursor: pointer;
  transition: transform 0.3s ease-in-out;
  margin: 20px auto;
}

button:hover {
  transform: scale(1.05);
}

.button-container {
  display: flex;
  flex-direction: row;
  justify-content: center;
  gap: 20px;
}

.guess {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 20px auto;
}

.guess-input {
  font-size: 1.2rem;
  padding: 10px;
  border: none;
  border-radius: 5px;
  background-color: rgba(255, 255, 255, 0.2);
  width: 200px;
  text-align: center;
  margin: 10px auto;
}

.correct-border {
  box-shadow: 0 0 10px 2px limegreen;
  border: 2px solid limegreen;
}

.incorrect-border {
  box-shadow: 0 0 10px 2px red;
  border: 2px solid red;
}

button:disabled,
.disabled-button {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

button.submit-button {
  background-color: #28a745; 
}
button.submit-button:hover {
  background-color: #218838;
}

button.nav-button {
  background-color: #007bff; 
}
button.nav-button:hover {
  background-color: #0069d9;
}

button.shuffle-button {
  background-color: #ff9800; 
}
button.shuffle-button:hover {
  background-color: #e68900;
}

button.submit-button,
button.nav-button,
button.shuffle-button {
  transition: transform 0.2s ease, background-color 0.2s ease;
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  color: black;
}

.overlay-content {
  background-color: rgba(255, 255, 255, 0.95);
  padding: 30px 50px;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 0 15px rgba(0,0,0,0.3);
}
