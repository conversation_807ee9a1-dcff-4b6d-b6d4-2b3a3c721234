import React from "react";
import "../App.css";

const APIForm = ({ inputs, handleChange, onSubmit }) => {
  return (
    <div>
      <h2>Select your Image Attributes:</h2>
      <form className="form-container" onSubmit={(e) => e.preventDefault()}>
        {/* URL */}
        <div className="form">
          <h2>URL</h2>
          <input
            type="text"
            name="url"
            value={inputs.url}
            placeholder="e.g. google.com"
            onChange={handleChange}
            className="textbox"
          />
          <p>
            Enter the website URL you want to screenshot (without https://)
          </p>
        </div>

        {/* Format */}
        <div className="form">
          <h2>Image Format</h2>
          <select
            name="format"
            value={inputs.format}
            onChange={handleChange}
            className="textbox"
          >
            <option value="">Select a format</option>
            <option value="jpeg">JPEG</option>
            <option value="png">PNG</option>
            <option value="webp">WEBP</option>
          </select>
        </div>

        {/* No Ads */}
        <div className="form">
          <label>
            <input
              type="checkbox"
              name="no_ads"
              checked={inputs.no_ads === "true"}
              onChange={(e) =>
                handleChange({
                  target: {
                    name: "no_ads",
                    value: e.target.checked ? "true" : "false",
                  },
                })
              }
            />{" "}
            Remove Ads?
          </label>
        </div>

        {/* No Cookie Banners */}
        <div className="form">
          <label>
            <input
              type="checkbox"
              name="no_cookie_banners"
              checked={inputs.no_cookie_banners === "true"}
              onChange={(e) =>
                handleChange({
                  target: {
                    name: "no_cookie_banners",
                    value: e.target.checked ? "true" : "false",
                  },
                })
              }
            />{" "}
            Remove Cookie Banners?
          </label>
        </div>

        {/* Width */}
        <div className="form">
          <h2>Width (px)</h2>
          <input
            type="number"
            name="width"
            value={inputs.width}
            onChange={handleChange}
            className="textbox"
          />
        </div>

        {/* Height */}
        <div className="form">
          <h2>Height (px)</h2>
          <input
            type="number"
            name="height"
            value={inputs.height}
            onChange={handleChange}
            className="textbox"
          />
        </div>
      </form>

      <button type="submit" className="button" onClick={onSubmit}>
        Take that Pic! 🎞
      </button>
    </div>
  );
};

export default APIForm;
