# Web Development Project 3 - Minecraft Trivia Flashcards


Submitted by: **<PERSON><PERSON><PERSON><PERSON> Patil**

This web app: **is a flashcard-based quiz game designed to test users' knowledge of Minecraft. Users can input answers to trivia questions, navigate through a list of flashcards, and track their performance through a dynamic streak system. Cards can be shuffled, marked as mastered, and removed from the pool of active questions.**

Time spent: **5** hours spent in total

## Required Features

The following **required** functionality is completed:

- [x] **The user can enter their guess into an input box *before* seeing the flipside of the card**
  - Application features a clearly labeled input box with a submit button where users can type in a guess
  - Clicking on the submit button with an **incorrect** answer shows visual feedback that it is wrong 
  -  Clicking on the submit button with a **correct** answer shows visual feedback that it is correct
- [x] **The user can navigate through an ordered list of cardss**
  - A forward/next button displayed on the card navigates to the next card in a set sequence when clicked
  - A previous/back button displayed on the card returns to the previous card in the set sequence when clicked
  - Both the next and back buttons should have some visual indication that the user is at the beginning or end of the list (for example, graying out and no longer being available to click), not allowing for wrap-around navigation

The following **optional** features are implemented:


- [x] Users can use a shuffle button to randomize the order of the cards
  - Cards should remain in the same sequence (**NOT** randomized) unless the shuffle button is clicked 
  - Cards should change to a random sequence once the shuffle button is clicked
- [x] A user’s answer may be counted as correct even when it is slightly different from the target answer
  - Answers are considered correct even if they only partially match the answer on the card 
  - Examples: ignoring uppercase/lowercase discrepancies, ignoring punctuation discrepancies, matching only for a particular part of the answer rather than the whole answer
- [x] A counter displays the user’s current and longest streak of correct responses
  - The current counter increments when a user guesses an answer correctly
  - The current counter resets to 0 when a user guesses an answer incorrectly
  - A separate counter tracks the longest streak, updating if the value of the current streak counter exceeds the value of the longest streak counter 
- [x] A user can mark a card that they have mastered and have it removed from the pool of displayed cards
  - The user can mark a card to indicate that it has been mastered
  - Mastered cards are removed from the pool of displayed cards and added to a list of mastered cards


The following **additional** features are implemented:

* [x] Background dynamically adjusts based on each card's theme color, with legible contrasting text.

* [x] Reset button appears once all cards are mastered.

* [x] Images on card flip help reinforce learning through visual cues.



## Video Walkthrough

Here's a walkthrough of implemented user stories:

<img src='./src/assets/Week 3 Project 3.gif' title='Video Walkthrough' width='' alt='Video Walkthrough' />

GIF created with ScreenToGif  

## Notes

Describe any challenges encountered while building the app.
- Dynamically updating card state and navigation after cards were mastered.
- Ensuring answer input was flexible enough to accept near matches.



## License

    Copyright 2025 Prathamesh Patil

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.