* {
  background-color: rgb(45, 45, 45);
  font-family: "Lucida Sans", "Lucida Sans Regular", "Lucida Grande",
    "Lucida Sans Unicode", Geneva, Verdana, sans-serif;
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  color: white;
  padding: 10px;
}

.header img {
  height: auto;
  width: 20em;
}

.header img:hover {
  cursor: pointer;
  transform: scale(1.1);
  transition: transform 0.2s ease-in-out;
}

.container {
  display: flex;
  margin-top: 50px;
  justify-content: center;
}

.upgrade {
  border: 1px solid rgba(153, 153, 153, 0.497);
  border-radius: 10px;
  padding: 20px 10px;
  margin-right: 25px;
  width: 200px;
  color: rgba(255, 255, 255, 0.87);
  text-align: center;
  line-height: 0.5;
}

.upgrade button {
  border: 1px solid transparent;
  border-radius: 8px;
  padding: 0.3em 1em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  color: rgba(255, 255, 255, 0.87);
  cursor: pointer;
  margin-top: 10px;
}
