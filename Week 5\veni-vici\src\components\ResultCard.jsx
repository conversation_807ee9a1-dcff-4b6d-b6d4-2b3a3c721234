export default function ResultCard({ cat, onAttributeClick }) {
  if (!cat) return null;

  const { name, image, attributes } = cat;

  return (
    <div className="bg-gray-800/50 backdrop-blur-sm p-6 rounded-xl shadow-2xl w-full max-w-lg border border-gray-700">
      {/* Cat Name */}
      <h2 className="text-3xl font-bold mb-4 text-center text-white">{name}</h2>

      {/* Attributes as clickable buttons */}
      <div className="flex flex-wrap gap-3 justify-center mb-6">
        {attributes.map((attribute, index) => (
          <button
            key={`${attribute}-${index}`}
            onClick={() => onAttributeClick(attribute)}
            className="bg-gradient-to-r from-amber-500 to-yellow-500 hover:from-amber-600 hover:to-yellow-600 text-black px-4 py-2 rounded-full font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105 cursor-pointer"
          >
            {attribute}
          </button>
        ))}
      </div>

      {/* Cat Image */}
      <div className="relative overflow-hidden rounded-lg">
        <img
          src={image}
          alt={`${name} cat`}
          className="w-full h-64 object-cover rounded-lg shadow-lg hover:scale-105 transition-transform duration-300"
        />
      </div>
    </div>
  );
}
