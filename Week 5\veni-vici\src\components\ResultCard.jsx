export default function ResultCard({ data, img, onKeywordClick }) {
  const { title, keywords = [] } = data;

  return (
    <div className="bg-gray-800 p-4 rounded-lg shadow-md w-full max-w-md">
      <h2 className="text-2xl font-semibold mb-2">{title}</h2>
      <div className="flex flex-wrap gap-2 justify-center mb-4">
        {keywords.map((kw) => (
          <span
            key={kw}
            onClick={() => onKeywordClick(kw)}
            className="bg-yellow-700 hover:bg-yellow-600 text-white px-3 py-1 rounded cursor-pointer"
          >
            {kw}
          </span>
        ))}
      </div>
      <img src={img} alt={title} className="rounded-lg w-full" />
    </div>
  );
}
