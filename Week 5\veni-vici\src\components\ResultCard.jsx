export default function ResultCard({ cat, onAttributeClick }) {
  if (!cat) return null;

  const { name, image, attributes } = cat;

  return (
    <div className="bg-gray-800/50 backdrop-blur-sm p-4 rounded-xl shadow-2xl w-full border border-gray-700">
      <h2 className="text-2xl font-bold mb-3 text-center text-white">{name}</h2>

      <div className="flex flex-wrap gap-2 justify-center mb-4">
        {attributes.map((attribute, index) => (
          <button
            key={`${attribute}-${index}`}
            onClick={() => onAttributeClick(attribute)}
            className="bg-gradient-to-r from-amber-500 to-yellow-500 hover:from-amber-600 hover:to-yellow-600 text-black px-3 py-1 rounded-full text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105 cursor-pointer"
          >
            {attribute}
          </button>
        ))}
      </div>

      <div className="relative overflow-hidden rounded-lg">
        <img
          src={image}
          alt={`${name} cat`}
          className="main-cat-image mx-auto shadow-lg hover:scale-105 transition-transform duration-300"
        />
      </div>
    </div>
  );
}
