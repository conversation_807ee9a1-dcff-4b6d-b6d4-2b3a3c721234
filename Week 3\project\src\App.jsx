import React from "react";
import minecraftBg from "./assets/minecraft_bg.jpg";
import { useState } from "react";
import Card from "./components/Card";
import obsidian from "./assets/obsidian.jpg";
import creeper from "./assets/creeper.jpg";
import bones from "./assets/bones.png";
import diamondArmor from "./assets/diamondArmour.webp";
import pickaxe from "./assets/pickaxe.png";
import end from "./assets/end.webp";
import goldenApple from "./assets/golden_apple.webp";
import steak from "./assets/steak.webp";
import polarBear from "./assets/polar_bear.webp";
import frostWalker from "./assets/frwalkar.webp";
import "./App.css";

const App = () => {
  const minecraftCards = [
    {
      question: "What material is needed to build a Nether Portal?",
      answer: "Obsidian",
      color: "rgb(11, 12, 12)",
      image: obsidian,
    },
    {
      question: "Which mob explodes when it gets close to the player?",
      answer: "Creeper",
      color: "rgb(113, 147, 75)",
      image: creeper,
    },
    {
      question: "What do you use to tame a wolf?",
      answer: "Bones",
      color: "rgb(189, 189, 189)",
      image: bones,
    },
    {
      question: "How many diamonds are needed for a full set of diamond armor?",
      answer: "24",
      color: "rgb(159, 207, 255)",
      image: diamondArmor,
    },
    {
      question: "Which tool is best for mining stone blocks quickly?",
      answer: "Pickaxe",
      color: "rgb(69, 69, 69)",
      image: pickaxe,
    },
    {
      question:
        "Which dimension contains Endermen, End Cities, and the Ender Dragon?",
      answer: "The End",
      color: "rgb(11, 12, 12)",
      image: end,
    },
    {
      question: "What item do you need to cure a zombie villager?",
      answer: "A splash potion of Weakness and a golden apple",
      color: "rgb(255, 215, 0)",
      image: goldenApple,
    },
    {
      question: "What food item restores the most hunger points?",
      answer: "Steak",
      color: "rgb(103, 46, 0)",
      image: steak,
    },
    {
      question: "Which biome do polar bears spawn in?",
      answer: "Snowy tundra or ice plains",
      color: "rgb(202, 255, 255)",
      image: polarBear,
    },
    {
      question: "What enchantment allows you to walk on water by freezing it?",
      answer: "Frost Walker",
      color: "rgb(36, 236, 236)",
      image: frostWalker,
    },
  ];

  const [isFlipped, setIsFlipped] = useState(false);
  const [currentStreak, setCurrentStreak] = useState(0);
  const [longestStreak, setLongestStreak] = useState(0);
  const [userGuess, setUserGuess] = useState("");
  const [correct, setCorrect] = useState(false);
  const [hasSubmitted, setHasSubmitted] = useState(false);
  const [cardOrder, setCardOrder] = useState(minecraftCards.map((_, i) => i));
  const [currentPosition, setCurrentPosition] = useState(0);
  const [masteredCards, setMasteredCards] = useState([]);

  const handleGuessChange = (e) => {
    setUserGuess(e.target.value);
  };

  const handleCardClick = () => {
    setIsFlipped(!isFlipped);
  };

  const resetCardState = () => {
    setUserGuess("");
    setCorrect(false);
    setHasSubmitted(false);
  };

  const normalize = (str) => {
    return str
      .toLowerCase()
      .replace(/[.,/#!$%^&*;:{}=\-_`~()]/g, "") // remove punctuation
      .trim();
  };

  const handleSubmit = () => {
    setHasSubmitted(true);

    const normalizedGuess = normalize(userGuess);
    const normalizedAnswer = normalize(currentCard.answer);

    const isCorrect =
      normalizedAnswer.includes(normalizedGuess) ||
      normalizedGuess.includes(normalizedAnswer);

    if (isCorrect) {
      setCurrentStreak(currentStreak + 1);
      if (currentStreak + 1 > longestStreak) {
        setLongestStreak(currentStreak + 1);
      }
      setCorrect(true);
      setTimeout(() => {
        handleNextClick();
      }, 500);
    } else {
      setCurrentStreak(0);
      setCorrect(false);
    }
  };

  const handleNextClick = () => {
    if (isFlipped) {
      setIsFlipped(false);
      setTimeout(() => {
        if (currentPosition < cardOrder.length - 1) {
          setCurrentPosition(currentPosition + 1);
        }
        resetCardState();
      }, 500);
    } else {
      if (currentPosition < cardOrder.length - 1) {
        setCurrentPosition(currentPosition + 1);
      }
      resetCardState();
    }
  };

  const handlePreviousClick = () => {
    if (isFlipped) {
      setIsFlipped(false);
      setTimeout(() => {
        if (currentPosition > 0) {
          setCurrentPosition(currentPosition - 1);
        }
        resetCardState();
      }, 500);
    } else {
      if (currentPosition > 0) {
        setCurrentPosition(currentPosition - 1);
      }
      resetCardState();
    }
  };

  const handleShuffleClick = () => {
    const shuffled = [...minecraftCards.keys()].sort(() => Math.random() - 0.5);
    setCardOrder(shuffled);
    setCurrentPosition(0);
    setIsFlipped(false);
    resetCardState();
  };

  const handleMarkMastered = () => {
    const currentIndex = cardOrder[currentPosition];

    if (!masteredCards.includes(currentIndex)) {
      setMasteredCards([...masteredCards, currentIndex]);
    }

    const updatedCardOrder = cardOrder.filter(
      (index) => index !== currentIndex
    );
    setCardOrder(updatedCardOrder);

    if (currentPosition >= updatedCardOrder.length) {
      setCurrentPosition(updatedCardOrder.length - 1);
    }

    setIsFlipped(false);
    resetCardState();
  };

  const handleRestart = () => {
    setCardOrder(minecraftCards.map((_, i) => i));
    setCurrentPosition(0);
    setIsFlipped(false);
    setCurrentStreak(0);
    setLongestStreak(0);
    setUserGuess("");
    setCorrect(false);
    setHasSubmitted(false);
    setMasteredCards([]);
  };

  const currentIndex = cardOrder.length > 0 ? cardOrder[currentPosition] : null;
  const currentCard =
    currentIndex !== null ? minecraftCards[currentIndex] : null;

  // Function to determine text color based on background color
  const getContrastTextColor = (bgColor) => {
    const rgb = bgColor.match(/\d+/g).map(Number);
    const luminance = (0.299 * rgb[0] + 0.587 * rgb[1] + 0.114 * rgb[2]) / 255;
    return luminance > 0.5 ? "#000000" : "#ffffff";
  };

  const textColor = currentCard ? getContrastTextColor(currentCard.color) : "#000000"; 

  return (
    <>
      <img src={minecraftBg} alt="Minecraft background" className="bg" />
      {currentCard ? (
        <div className="main">
          {isFlipped && currentCard.image && (
            <img
              src={currentCard.image}
              alt={currentCard.question}
              style={{
                transform: "scaleX(-1)",
                height: "250px",
                width: "auto",
              }}
              className="side-image"
            />
          )}
          <div
            className="container"
            style={{ backgroundColor: currentCard.color }}
          >
            <h1 style={{ color: textColor }}>Minecraft Trivia Flashcards</h1>
            <p style={{ color: textColor }}>
              Click the card to flip. Test your Minecraft knowledge!
            </p>
            <p style={{ color: textColor }}>
              Total Cards: {minecraftCards.length}
              <br />
              Longest Streak: {longestStreak}{" "}
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Current Streak:{" "}
              {currentStreak}
            </p>

            <Card
              frontText={currentCard.question}
              backText={currentCard.answer}
              flipped={isFlipped}
              onClick={handleCardClick}
            />

            <div className="guess" style={{ color: textColor }}>
              Guess the answer here:
              <input
                style={{ color: textColor }}
                type="text"
                className={`guess-input ${
                  hasSubmitted && hasSubmitted
                    ? correct
                      ? "correct-border"
                      : "incorrect-border"
                    : ""
                }`}
                onChange={handleGuessChange}
                value={userGuess}
              />
            </div>

            <span className="button-container">
              <button
                onClick={handlePreviousClick}
                disabled={currentPosition === 0}
                className={`nav-button ${
                  currentPosition === 0 ? "disabled-button" : ""
                }`}
              >
                Previous
              </button>
              <button onClick={handleSubmit} className="submit-button">
                Submit
              </button>
              <button
                onClick={handleNextClick}
                disabled={currentPosition === cardOrder.length - 1}
                className={`nav-button ${
                  currentPosition === cardOrder.length - 1
                    ? "disabled-button"
                    : ""
                }`}
              >
                Next
              </button>
            </span>
            <span className="button-container">
              <button className="shuffle-button" onClick={handleShuffleClick}>
                Shuffle Cards
              </button>
              <button
                onClick={handleMarkMastered}
                className="mastered-button"
                disabled={cardOrder.length === 0}
              >
                Mark as Mastered
              </button>
            </span>
          </div>
          {isFlipped && currentCard.image && (
            <img
              src={currentCard.image}
              alt={currentCard.question}
              style={{ height: "250px", width: "auto" }}
              className="side-image"
            />
          )}
        </div>
      ) : null}
      {cardOrder.length === 0 && (
        <div className="overlay">
          <div className="overlay-content">
            <h1>🎉 You have mastered all cards! 🎉</h1>
            <button className="shuffle-button" onClick={handleRestart}>
              Restart
            </button>
          </div>
        </div>
      )}
    </>
  );
};

export default App;
