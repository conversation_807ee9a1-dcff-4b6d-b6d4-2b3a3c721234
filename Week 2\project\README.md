# Web Development Project 2 - *Minecraft Trivia Flashcards*

Submitted by: **<PERSON><PERSON><PERSON><PERSON> Patil**

This web app: **A fun and interactive flashcard app that challenges users on their Minecraft knowledge. Each card features a trivia question on the front, and reveals the answer — along with an image — on the back. Users can flip cards and view a new random question with each click.**

Time spent: **4** hours spent in total

## Required Features

The following **required** functionality is completed:


- [x] **The app displays the title of the card set, a short description, and the total number of cards**
  - [x] Title of card set is displayed 
  - [x] A short description of the card set is displayed 
  - [x] A list of card pairs is created
  - [x] The total number of cards in the set is displayed 
  - [x] Card set is represented as a list of card pairs (an array of dictionaries where each dictionary contains the question and answer is perfectly fine)
- [x] **A single card at a time is displayed**
  - [x] Only one half of the information pair is displayed at a time
- [x] **Clicking on the card flips the card over, showing the corresponding component of the information pair**
  - [x] Clicking on a card flips it over, showing the back with corresponding information 
  - [x] Clicking on a flipped card again flips it back, showing the front
- [x] **Clicking on the next button displays a random new card**

The following **optional** features are implemented:

- [x] Cards contain images in addition to or in place of text
  - [x] Some or all cards have images in place of or in addition to text
- [x] Cards have different visual styles such as color based on their category
  - Example categories you can use:
    - Difficulty: Easy/medium/hard
    - Subject: Biology/Chemistry/Physics/Earth science

The following **additional** features are implemented:

* [x] Dynamic text contrast (AI-assisted) ensures accessibility regardless of background color
* [x] Smooth flipping animation using CSS transitions
* [x] Side-by-side images shown when the card is flipped, mirrored for visual symmetry

## Video Walkthrough

Here's a walkthrough of implemented required features:

<img src='./src/assets/Week 2 Project 2.gif' title='Video Walkthrough' width='' alt='Video Walkthrough' />

GIF created with ScreenToGif
## Notes

Describe any challenges encountered while building the app. (AI-assisted me with both)
- Implementing a mirrored image layout while keeping it responsive
- Ensuring proper contrast between text and background across all card colors

## License

    Copyright 2025 Prathamesh Patil

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.