# Web Development Project 1 - *DSA Vault*

Submitted by: **<PERSON><PERSON><PERSON>sh Patil**

This web app: **DSA Vault is a curated collection of video resources focused on Data Structures and Algorithms (DSA).**

Time spent: **5** hours spent in total

## Required Features

The following **required** functionality is completed:

- [x] **The app has a cohesive, unique theme for events or resources relevant to a specific community**
  - [x] Header/title describing the theme is displayed
- [x] **At least 10 unique events or resources are displayed in a responsive card format**
  - [x] There are at least 10 cards displayed 
  - [x] The cards should be displayed in an organized format (ex. a grid, or in one line)
  - [x] Each card should include some information about the event or resource


The following **optional** features are implemented:

- [x] Buttons or links to a related resources are on each card component
  - [x] All cards have buttons or links in addition to text
- [x] The site is responsive for both desktop and mobile formats
  - [x] Web app is shown in a mobile format

The following **additional** features are implemented:

* [x] Custom styling with a modern UI color palette (gradient header, shadowed cards, interactive buttons)
* [x] Footer with attributions

## Video Walkthrough

Here's a walkthrough of implemented required features:

<img src="./src/assets/Week 1 Project 1.gif" title="Video Walkthrough" alt="Video Walkthrough" />

GIF created with ScreenToGif 

## Notes

Describe any challenges encountered while building the app.
- Structuring the card layout to be fully responsive. (Took MDN Reference for media query)
- Choosing the right balance of visual design

## License

    Copyright 2025 Prathamesh Patil

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
