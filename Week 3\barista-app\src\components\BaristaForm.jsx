import React, { Component, useState } from "react";
import RecipeChoices from "./ReciepeChoices";
import drinksJson from "./drinks.json";

const BaristaForm = () => {
  const [inputs, setInputs] = useState({
    temperature: "",
    milk: "",
    syrup: "",
    blended: "",
  });

  const [currentDrink, setCurrentDrink] = useState("");
  const [trueRecipe, setTrueRecipe] = useState({
    temp: "",
    syrup: "",
    milk: "",
    blended: "",
  });

  const [correct_temp, setCheckedTemperature] = useState("");
  const [correct_syrup, setCheckedSyrup] = useState("");
  const [correct_milk, setCheckedMilk] = useState("");
  const [correct_blended, setCheckedBlended] = useState("");

  const ingredients = {
    temperature: ["hot", "lukewarm", "cold"],
    syrup: ["mocha", "vanilla", "toffee", "maple", "caramel", "other", "none"],
    milk: ["cow", "oat", "goat", "almond", "none"],
    blended: ["yes", "turbo", "no"],
  };

  // const onCheckAnswer = () => {
  //   if (trueRecipe.temp != inputs["temperature"]) {
  //     setCheckedTemperature("wrong");
  //   } else {
  //     setCheckedTemperature("correct");
  //   }
  //   if (trueRecipe.syrup != inputs["syrup"]) {
  //     setCheckedSyrup("wrong");
  //   } else {
  //     setCheckedSyrup("correct");
  //   }
  //   if (trueRecipe.milk != inputs["milk"]) {
  //     setCheckedMilk("wrong");
  //   } else {
  //     setCheckedMilk("correct");
  //   }
  //   if (trueRecipe.blended != inputs["blended"]) {
  //     setCheckedBlended("wrong");
  //   } else {
  //     setCheckedBlended("correct");
  //   }
  // };

  const onCheckAnswer = () => {
    if (!ingredients.temperature.includes(inputs.temperature)) {
      alert("Invalid temperature choice!");
      return;
    }
    if (!ingredients.syrup.includes(inputs.syrup)) {
      alert("Invalid syrup choice!");
      return;
    }
    if (!ingredients.milk.includes(inputs.milk)) {
      alert("Invalid milk choice!");
      return;
    }
    if (!ingredients.blended.includes(inputs.blended)) {
      alert("Invalid blended choice!");
      return;
    }

    // OLD LOGIC: Answer check
    if (trueRecipe.temp !== inputs["temperature"]) {
      setCheckedTemperature("wrong");
    } else {
      setCheckedTemperature("correct");
    }

    if (trueRecipe.syrup !== inputs["syrup"]) {
      setCheckedSyrup("wrong");
    } else {
      setCheckedSyrup("correct");
    }

    if (trueRecipe.milk !== inputs["milk"]) {
      setCheckedMilk("wrong");
    } else {
      setCheckedMilk("correct");
    }

    if (trueRecipe.blended !== inputs["blended"]) {
      setCheckedBlended("wrong");
    } else {
      setCheckedBlended("correct");
    }
  };

  const onNewDrink = () => {
    setCheckedTemperature("");
    setCheckedSyrup("");
    setCheckedMilk("");
    setCheckedBlended("");

    setInputs({
      temperature: "",
      milk: "",
      syrup: "",
      blended: "",
    });

    getNextDrink();
  };

  const getNextDrink = () => {
    let randomDrinkIndex = Math.floor(Math.random() * drinksJson.drinks.length);
    setCurrentDrink(drinksJson.drinks[randomDrinkIndex].name);
    setTrueRecipe(drinksJson.drinks[randomDrinkIndex].ingredients);
  };

  return (
    <div className="form">
      <h2>Hi, I'd like to order a:</h2>
      <div className="drink-container">
        <h2 className="mini-header">{currentDrink}</h2>
        <button
          type="new-drink-button"
          className="button newdrink"
          onClick={onNewDrink}
        >
          🔄
        </button>
      </div>
      <form className="container">
        <div className="mini-container">
          <h3>Temperature</h3>
          <div className="answer-space" id={correct_temp}>
            {inputs["temperature"]}
          </div>
          <RecipeChoices
            handleChange={(e) =>
              setInputs((prevState) => ({
                ...prevState,
                // [e.target.name]: e.target.value,
                [e.target.name]: e.target.value.toLowerCase(),
              }))
            }
            label="temperature"
            choices={ingredients["temperature"]}
            // checked={inputs["temperature"]}
            currentVal={inputs["temperature"]}
          />
        </div>
        <div className="mini-container">
          <h3>Syrup</h3>
          <div className="answer-space" id={correct_syrup}>
            {inputs["syrup"]}
          </div>
          <RecipeChoices
            handleChange={(e) =>
              setInputs((prevState) => ({
                ...prevState,
                // [e.target.name]: e.target.value,
                [e.target.name]: e.target.value.toLowerCase()
              }))
            }
            label="syrup"
            choices={ingredients["syrup"]}
            // checked={inputs["syrup"]}
            currentVal={inputs["syrup"]}
          />
        </div>
        <div className="mini-container">
          <h3>Milk</h3>
          <div className="answer-space" id={correct_milk}>
            {inputs["milk"]}
          </div>
          <RecipeChoices
            handleChange={(e) =>
              setInputs((prevState) => ({
                ...prevState,
                // [e.target.name]: e.target.value,
                [e.target.name]: e.target.value.toLowerCase()
              }))
            }
            label="milk"
            choices={ingredients["milk"]}
            // checked={inputs["milk"]}
            currentVal={inputs["milk"]}
          />
        </div>
        <div className="mini-container">
          <h3>Blended</h3>
          <div className="answer-space" id={correct_blended}>
            {inputs["blended"]}
          </div>
          <RecipeChoices
            handleChange={(e) =>
              setInputs((prevState) => ({
                ...prevState,
                // [e.target.name]: e.target.value,
                [e.target.name]: e.target.value.toLowerCase()
              }))
            }
            label="blended"
            choices={ingredients["blended"]}
            // checked={inputs["blended"]}
            currentVal={inputs["blended"]}
          />
        </div>
      </form>
      <button type="submit" className="button submit" onClick={onCheckAnswer}>
        Check Answer
      </button>
    </div>
  );
};

export default BaristaForm;
